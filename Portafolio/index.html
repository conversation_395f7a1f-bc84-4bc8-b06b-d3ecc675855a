<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portafolio R-exams ICFES | Especialista en Evaluación Matemática</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-text: #2c3e50;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark-text);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-color), var(--success-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }
        
        .exercise-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }
        
        .exercise-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .exercise-header {
            padding: 25px;
            background: linear-gradient(135deg, var(--light-bg), white);
            border-bottom: 3px solid var(--secondary-color);
        }
        
        .competency-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .badge-interpretacion { background: #e8f5e8; color: #27ae60; }
        .badge-formulacion { background: #e3f2fd; color: #2196f3; }
        .badge-razonamiento { background: #fff3e0; color: #ff9800; }
        
        .difficulty-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .difficulty-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ddd;
        }
        
        .difficulty-dot.active {
            background: var(--warning-color);
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .tech-badge {
            background: var(--primary-color);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .stats-section {
            background: var(--light-bg);
            padding: 80px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--secondary-color);
            display: block;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: var(--dark-text);
            margin-top: 10px;
        }
        
        .contact-section {
            background: var(--primary-color);
            color: white;
            padding: 80px 0;
        }
        
        .contact-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            color: var(--dark-text);
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, var(--secondary-color), var(--success-color));
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
        }
        
        .navbar-custom {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .navbar-custom.scrolled {
            background: rgba(44, 62, 80, 0.98);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0;
            }
            
            .feature-card {
                margin-bottom: 30px;
            }
            
            .stat-number {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#home">
                <i class="fas fa-calculator me-2"></i>R-exams ICFES Pro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Inicio</a></li>
                    <li class="nav-item"><a class="nav-link" href="#servicios">Servicios</a></li>
                    <li class="nav-item"><a class="nav-link" href="#ejercicios">Ejercicios Demo</a></li>
                    <li class="nav-item"><a class="nav-link" href="#estadisticas">Estadísticas</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contacto">Contacto</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold mb-4">
                            Especialista en <span class="text-warning">R-exams</span> para ICFES
                        </h1>
                        <p class="lead mb-4">
                            Desarrollo ejercicios matemáticos automatizados de alta calidad para evaluaciones ICFES, 
                            utilizando tecnologías avanzadas como R, Python, LaTeX y TikZ.
                        </p>
                        <div class="d-flex flex-wrap gap-3">
                            <a href="#ejercicios" class="btn btn-primary-custom btn-lg">
                                <i class="fas fa-eye me-2"></i>Ver Ejercicios Demo
                            </a>
                            <a href="#contacto" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-envelope me-2"></i>Solicitar Cotización
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-chart-line" style="font-size: 15rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="servicios" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4">Servicios Especializados</h2>
                    <p class="lead text-muted">
                        Ofrezco soluciones completas para la creación de evaluaciones matemáticas automatizadas
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Bancos de Preguntas Automatizados</h4>
                        <p class="text-muted mb-4">
                            Desarrollo sistemas que generan cientos de versiones únicas de ejercicios matemáticos 
                            con validación automática y distractores inteligentes.
                        </p>
                        <div class="tech-stack">
                            <span class="tech-badge">R-exams</span>
                            <span class="tech-badge">300+ versiones</span>
                            <span class="tech-badge">Validación automática</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Visualizaciones Avanzadas</h4>
                        <p class="text-muted mb-4">
                            Creo gráficos dinámicos y diagramas matemáticos precisos usando Python/matplotlib, 
                            TikZ y herramientas de visualización profesionales.
                        </p>
                        <div class="tech-stack">
                            <span class="tech-badge">Python</span>
                            <span class="tech-badge">TikZ</span>
                            <span class="tech-badge">Matplotlib</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Consultoría ICFES</h4>
                        <p class="text-muted mb-4">
                            Asesoría especializada en competencias ICFES, diseño de evaluaciones y 
                            implementación de sistemas de evaluación automatizada.
                        </p>
                        <div class="tech-stack">
                            <span class="tech-badge">Competencias ICFES</span>
                            <span class="tech-badge">Metodología</span>
                            <span class="tech-badge">Capacitación</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Exercises Section -->
    <section id="ejercicios" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4">Ejercicios Demo</h2>
                    <p class="lead text-muted">
                        Ejemplos representativos de mi trabajo en diferentes competencias ICFES
                    </p>
                </div>
            </div>
            <div id="ejercicios-container">
                <!-- Los ejercicios se cargarán dinámicamente aquí -->
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section id="estadisticas" class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4">Estadísticas del Proyecto</h2>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">300+</span>
                        <div class="stat-label">Versiones por Ejercicio</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <div class="stat-label">Ejercicios Desarrollados</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">3</span>
                        <div class="stat-label">Competencias ICFES</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <div class="stat-label">Automatización</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contacto" class="contact-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4">¿Listo para Colaborar?</h2>
                    <p class="lead">
                        Contacta conmigo para discutir tu proyecto de evaluación matemática
                    </p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form">
                        <form id="contact-form">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="nombre" class="form-label">Nombre *</label>
                                    <input type="text" class="form-control" id="nombre" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="institucion" class="form-label">Institución</label>
                                    <input type="text" class="form-control" id="institucion">
                                </div>
                                <div class="col-md-6">
                                    <label for="presupuesto" class="form-label">Presupuesto Estimado</label>
                                    <select class="form-control" id="presupuesto">
                                        <option value="">Seleccionar rango</option>
                                        <option value="500-1000">$500 - $1,000 USD</option>
                                        <option value="1000-2500">$1,000 - $2,500 USD</option>
                                        <option value="2500-5000">$2,500 - $5,000 USD</option>
                                        <option value="5000+">$5,000+ USD</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="proyecto" class="form-label">Descripción del Proyecto *</label>
                                    <textarea class="form-control" id="proyecto" rows="4" required 
                                              placeholder="Describe tu proyecto: tipo de ejercicios, competencias ICFES, cantidad estimada, plazos, etc."></textarea>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary-custom btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Enviar Solicitud
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 R-exams ICFES Pro. Especialista en Evaluación Matemática.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-github"></i></a>
                        <a href="#" class="text-light"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="js/ejercicios-data.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
