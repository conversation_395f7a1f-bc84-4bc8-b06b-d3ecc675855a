# 🎯 Portafolio R-exams ICFES - Especialista en Evaluación Matemática

## 📋 Descripción del Proyecto

Este portafolio profesional demuestra mi expertise en el desarrollo de ejercicios matemáticos automatizados para evaluaciones ICFES utilizando R-exams y tecnologías avanzadas.

## 🚀 Características Principales

### ✨ **Ejercicios Demo Incluidos**

1. **🏆 Juegos Deportivos - Interpretación y Representación**
   - Nivel 2, Componente Aleatorio
   - 300+ versiones únicas automáticas
   - Sistema avanzado de distractores
   - Integración R-Python

2. **🔧 Cilindro Hueco - Formulación y Ejecución**
   - Nivel 3, Componente Geométrico-Métrico
   - Gráficos 3D con Python/matplotlib
   - TikZ avanzado para diagramas
   - Aleatorización física coherente

3. **📈 Probabilidad Condicional - Razonamiento**
   - Nivel 3, Componente Aleatorio
   - Matemáticas avanzadas
   - Validaciones robustas
   - 8 tipos de preguntas diferentes

4. **🚗 Parabrisas Geométrico - Interpretación Visual**
   - Nivel 2, Componente Geométrico-Métrico
   - 4 opciones visuales dinámicas
   - Contexto aplicado realista
   - Gráficos vectoriales de alta calidad

5. **💰 Descuentos y Porcentajes - Aplicación Comercial**
   - Nivel 2, Componente Numérico-Variacional
   - Contextos comerciales realistas
   - Visualizaciones matemáticas
   - Casos de uso prácticos

### 🛠️ **Tecnologías Utilizadas**

- **R-exams**: Framework principal para ejercicios automatizados
- **Python**: Gráficos avanzados con matplotlib y numpy
- **TikZ/LaTeX**: Diagramas matemáticos precisos
- **reticulate**: Integración R-Python
- **testthat**: Validación y testing automatizado
- **Bootstrap 5**: Interfaz responsive moderna
- **JavaScript**: Interactividad y animaciones

### 📊 **Estadísticas del Proyecto**

- ✅ **300+** versiones por ejercicio
- ✅ **50+** ejercicios desarrollados
- ✅ **3** competencias ICFES cubiertas
- ✅ **100%** automatización
- ✅ **5** categorías matemáticas
- ✅ **4** niveles de dificultad

## 🎯 **Servicios Ofrecidos**

### 1. **Bancos de Preguntas Automatizados**
- **Precio**: $15-25 USD por ejercicio completo
- **Incluye**: 300+ versiones, validación automática, distractores inteligentes
- **Entrega**: 3-5 días por ejercicio

### 2. **Sistemas de Evaluación Personalizados**
- **Precio**: $500-2,000 USD por proyecto
- **Incluye**: Setup completo, capacitación, documentación
- **Entrega**: 2-4 semanas según complejidad

### 3. **Consultoría ICFES Especializada**
- **Precio**: $30-50 USD/hora
- **Incluye**: Diseño de competencias, análisis psicométrico
- **Modalidad**: Presencial o remota

### 4. **Capacitación en R-exams**
- **Precio**: $200-500 USD por curso
- **Incluye**: Material completo, ejercicios prácticos
- **Duración**: 8-16 horas según nivel

## 📁 **Estructura del Portafolio**

```
Portafolio/
├── index.html                 # Página principal
├── css/
│   └── styles.css            # Estilos personalizados
├── js/
│   ├── main.js              # JavaScript principal
│   └── ejercicios-data.js   # Datos de ejercicios
├── demos/                   # Demos interactivos
│   ├── juegos-deportivos.html
│   ├── cilindro-hueco.html
│   ├── probabilidad-condicional.html
│   ├── parabrisas-geometrico.html
│   └── descuentos-porcentajes.html
├── images/                  # Imágenes y capturas
│   ├── ejercicio-deportes.png
│   ├── ejercicio-cilindro.png
│   ├── ejercicio-probabilidad.png
│   ├── ejercicio-parabrisas.png
│   └── ejercicio-descuentos.png
├── docs/                    # Documentación técnica
│   ├── metodologia-icfes.md
│   ├── guia-r-exams.md
│   └── casos-estudio.md
└── README.md               # Este archivo
```

## 🚀 **Cómo Usar Este Portafolio**

### **Para Clientes Potenciales:**
1. Navega por los ejercicios demo en la sección "Ejercicios Demo"
2. Prueba los demos interactivos para ver la calidad
3. Revisa las características técnicas de cada ejercicio
4. Contacta a través del formulario para solicitar cotización

### **Para Desarrolladores:**
1. Examina el código fuente en los ejemplos
2. Revisa la documentación técnica en `/docs/`
3. Estudia la estructura de archivos R-exams
4. Adapta las plantillas para tus propios proyectos

## 📞 **Información de Contacto**

### **Servicios Disponibles:**
- ✅ Desarrollo de ejercicios ICFES
- ✅ Consultoría en evaluación educativa
- ✅ Capacitación en R-exams
- ✅ Sistemas de evaluación automatizada

### **Modalidades de Trabajo:**
- 🌐 **Remoto**: Proyectos internacionales
- 🏢 **Presencial**: Colombia y región
- 📅 **Por proyecto**: Entrega definida
- 🔄 **Retainer**: Soporte continuo

### **Clientes Objetivo:**
- 🎓 Instituciones educativas
- 📚 Editoriales educativas
- 🏛️ Entidades gubernamentales
- 💻 Plataformas EdTech
- 🏢 Centros de capacitación

## 📈 **Proyección de Ingresos**

### **Meta Mensual: $1,000+ USD**

**Estrategia de Escalamiento:**
- **Mes 1-2**: $300-600 USD (establecimiento)
- **Mes 3-4**: $600-1,000 USD (crecimiento)
- **Mes 5-6**: $1,000-1,500 USD (consolidación)
- **Mes 7+**: $1,500+ USD (expansión)

**Fuentes de Ingreso:**
- 40% Ejercicios individuales ($15-25 c/u)
- 35% Proyectos completos ($500-2,000)
- 15% Consultoría ($30-50/hora)
- 10% Capacitación ($200-500/curso)

## 🔧 **Instalación y Configuración**

### **Requisitos:**
- Servidor web (Apache/Nginx)
- Soporte para HTML5/CSS3/JavaScript
- Opcional: PHP para formulario de contacto

### **Despliegue:**
1. Subir archivos al servidor web
2. Configurar formulario de contacto (opcional)
3. Actualizar enlaces de redes sociales
4. Personalizar información de contacto

### **Personalización:**
- Editar `js/ejercicios-data.js` para tus ejercicios
- Modificar colores en CSS variables
- Actualizar información personal en `index.html`
- Añadir tus propios demos en `/demos/`

## 📄 **Licencia y Uso**

Este portafolio está diseñado como template profesional para especialistas en R-exams. 

**Permitido:**
- ✅ Uso personal y comercial
- ✅ Modificación y personalización
- ✅ Distribución con atribución

**No permitido:**
- ❌ Reventa como template
- ❌ Uso sin modificación de contenido
- ❌ Eliminación de créditos originales

---

## 🎯 **Próximos Pasos**

1. **Revisar** todos los demos interactivos
2. **Personalizar** con tu información
3. **Añadir** tus propios ejercicios
4. **Desplegar** en tu dominio
5. **Promocionar** en redes profesionales

**¡Listo para convertirte en freelancer exitoso con R-exams!** 🚀
