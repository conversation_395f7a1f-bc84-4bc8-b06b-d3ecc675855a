{"portafolio": {"titulo": "R-exams ICFES Pro - Especialista en Evaluación Matemática", "descripcion": "Desarrollo ejercicios matemáticos automatizados de alta calidad para evaluaciones ICFES", "version": "1.0.0", "autor": {"nombre": "[Tu Nombre]", "email": "[<EMAIL>]", "telefono": "[+57 XXX XXX XXXX]", "linkedin": "[tu-perfil-linkedin]", "github": "[tu-usuario-github]", "ubicacion": "Colombia"}}, "servicios": {"ejercicios_individuales": {"basico": {"precio": 15, "moneda": "USD", "tiempo_entrega": "2-3 días", "versiones": "100+", "revisiones": 1}, "avanzado": {"precio": 25, "moneda": "USD", "tiempo_entrega": "3-5 días", "versiones": "300+", "revisiones": 2}, "premium": {"precio": 40, "moneda": "USD", "tiempo_entrega": "5-7 días", "versiones": "300+", "revisiones": 3, "soporte_dias": 30}}, "paquetes": {"basico": {"ejercicios": 10, "precio": 120, "descuento": "20%", "tiempo_entrega": "2-3 semanas"}, "profesional": {"ejercicios": 25, "precio": 450, "descuento": "25%", "tiempo_entrega": "4-6 semanas"}, "institucional": {"ejercicios": 50, "precio": 800, "descuento": "35%", "tiempo_entrega": "8-10 semanas"}}, "sistemas_completos": {"basico": {"precio": 500, "ejercicios": 20, "tiempo_entrega": "2-3 semanas", "capacitacion_horas": 2, "soporte_meses": 1}, "profesional": {"precio": 1200, "ejercicios": 50, "tiempo_entrega": "4-6 semanas", "capacitacion_horas": 6, "soporte_meses": 3}, "enterprise": {"precio": 2500, "ejercicios": "100+", "tiempo_entrega": "8-12 semanas", "capacitacion_completa": true, "soporte_meses": 6}}, "consultoria": {"precio_hora": 50, "moneda": "USD", "modalidades": ["remota", "presencial"]}, "capacitacion": {"basica": {"precio": 300, "duracion_horas": 8, "modalidades": ["presencial", "virtual"]}, "avanzada": {"precio": 600, "duracion_horas": 16, "modalidades": ["presencial", "virtual"], "soporte_dias": 30}}}, "competencias_icfes": {"interpretacion_representacion": {"nombre": "Interpretación y Representación", "descripcion": "Aná<PERSON><PERSON> de gráficos, tablas y representaciones matemáticas", "color": "#27ae60", "ejemplos": ["Análisis de datos estadísticos", "Interpretación de gráficos", "Representaciones geométricas"]}, "formulacion_ejecucion": {"nombre": "Formulación y Ejecución", "descripcion": "Problemas de aplicación práctica y cálculos matemáticos", "color": "#2196f3", "ejemplos": ["<PERSON><PERSON><PERSON><PERSON>los geométric<PERSON>", "Problemas de aplicación", "Procedimientos algorítmicos"]}, "razonamiento": {"nombre": "Razonamiento", "descripcion": "Argumentación matemática y pensamiento crítico", "color": "#ff9800", "ejemplos": ["Demostración de propiedades", "<PERSON><PERSON><PERSON><PERSON>", "Argumentación lógica"]}}, "tecnologias": {"principales": [{"nombre": "R-exams", "descripcion": "Framework principal para ejercicios automatizados", "nivel": "Experto"}, {"nombre": "Python", "descripcion": "Gráficos avanzados con matplotlib y numpy", "nivel": "<PERSON><PERSON><PERSON>"}, {"nombre": "TikZ/LaTeX", "descripcion": "Diagramas matemáticos precisos", "nivel": "<PERSON><PERSON><PERSON>"}, {"nombre": "reticulate", "descripcion": "Integración R-Python", "nivel": "Intermedio"}], "complementarias": ["testthat", "ggplot2", "knitr", "Bootstrap", "JavaScript"]}, "estadisticas": {"versiones_por_ejercicio": "300+", "ejercicios_desarrollados": "50+", "competencias_icfes": 3, "automatizacion": "100%", "categorias_matematicas": 5, "niveles_dificultad": 4}, "formatos_salida": ["PDF", "Word", "HTML", "Moodle XML", "QTI", "<PERSON><PERSON>"], "garantias": {"calidad": {"funcionamiento_correcto": true, "revisiones_ilimitadas": true, "cumplimiento_icfes": true, "reembolso_disponible": true}, "soporte": {"email_response_hours": 24, "bug_resolution_hours": 72, "on_time_delivery": "95%", "availability": "99.5%"}}, "metodos_pago": ["PayPal", "Transferencia bancaria", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "modalidades_pago": {"pago_unico": {"descuento": "5%"}, "50_50": {"descripcion": "50% inicio, 50% entrega"}, "mensual": {"descripcion": "Para proyectos grandes"}, "retainer": {"rango": "$500-1500/mes", "descripcion": "Soporte continuo"}}, "clientes_objetivo": ["Instituciones educativas", "Editoriales educativas", "Entidades gubernamentales", "Plataformas EdTech", "Centros de capacitación"], "proyeccion_ingresos": {"mes_1_2": "$300-600", "mes_3_4": "$600-1000", "mes_5_6": "$1000-1500", "mes_7_plus": "$1500+"}, "fuentes_ingreso": {"ejercicios_individuales": "40%", "proyectos_completos": "35%", "consultoria": "15%", "capacitacion": "10%"}, "configuracion_sitio": {"colores": {"primario": "#2c3e50", "secundario": "#3498db", "exito": "#27ae60", "advertencia": "#f39c12", "peligro": "#e74c3c"}, "fuentes": {"principal": "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif", "codigo": "'Courier New', monospace"}, "animaciones": {"duracion_rapida": "0.2s", "duracion_media": "0.3s", "duracion_lenta": "0.5s"}}, "seo": {"palabras_clave": ["R-exams", "ICFES", "ejer<PERSON><PERSON>s matemá<PERSON>", "evaluación automatizada", "Python matemáticas", "TikZ", "LaTeX educativo", "freelancer <PERSON><PERSON><PERSON><PERSON><PERSON>"], "descripcion_meta": "Especialista en desarrollo de ejercicios matemáticos automatizados para evaluaciones ICFES usando R-exams, Python y tecnologías avanzadas.", "titulo_meta": "R-exams ICFES Pro | Especialista en Evaluación Matemática Automatizada"}, "redes_sociales": {"linkedin": {"url": "[tu-perfil-linkedin]", "activo": true}, "github": {"url": "[tu-usuario-github]", "activo": true}, "twitter": {"url": "[tu-usuario-twitter]", "activo": false}}, "analytics": {"google_analytics": "[GA-ID]", "google_tag_manager": "[GTM-ID]", "facebook_pixel": "[FB-PIXEL-ID]"}}