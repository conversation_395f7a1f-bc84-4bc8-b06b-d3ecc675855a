<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300">
  <defs>
    <linearGradient id="geometryBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#geometryBg)"/>
  
  <!-- Parabrisas geométrico -->
  <!-- Forma del parabrisas -->
  <path d="M 120 120 Q 200 80 280 120 L 280 200 Q 200 220 120 200 Z" 
        fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
  
  <!-- Líneas de medición -->
  <line x1="120" y1="120" x2="280" y2="120" stroke="white" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="120" y1="200" x2="280" y2="200" stroke="white" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="200" y1="80" x2="200" y2="220" stroke="white" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Ángulos -->
  <path d="M 130 120 A 10 10 0 0 1 140 110" fill="none" stroke="white" stroke-width="2"/>
  <path d="M 270 120 A 10 10 0 0 0 260 110" fill="none" stroke="white" stroke-width="2"/>
  
  <!-- Símbolos de ángulo recto -->
  <rect x="195" y="115" width="10" height="10" fill="none" stroke="white" stroke-width="1"/>
  
  <!-- Dimensiones -->
  <text x="200" y="105" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">
    h
  </text>
  <text x="200" y="240" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">
    base
  </text>
  <text x="340" y="160" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">
    α
  </text>
  
  <!-- Título -->
  <text x="200" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">
    Parabrisas Geométrico
  </text>
  <text x="200" y="55" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="rgba(255,255,255,0.9)">
    Interpretación Visual
  </text>
  
  <!-- Badge de nivel -->
  <rect x="320" y="15" width="60" height="25" rx="12" fill="rgba(255,255,255,0.2)"/>
  <text x="350" y="32" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">
    Nivel 2
  </text>
  
  <!-- Opciones visuales -->
  <rect x="50" y="250" width="80" height="30" rx="5" fill="rgba(255,255,255,0.2)"/>
  <text x="90" y="268" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white">
    4 opciones
  </text>
  <rect x="140" y="250" width="80" height="30" rx="5" fill="rgba(255,255,255,0.2)"/>
  <text x="180" y="268" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white">
    dinámicas
  </text>
  <rect x="230" y="250" width="80" height="30" rx="5" fill="rgba(255,255,255,0.2)"/>
  <text x="270" y="268" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white">
    Python
  </text>
</svg>
