<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300">
  <defs>
    <linearGradient id="cylinderBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196f3;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1976d2;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="cylinderGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4682B4;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#EBF5FF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#4682B4;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#cylinderBg)"/>
  
  <!-- Cilindro 3D -->
  <!-- Elipse superior -->
  <ellipse cx="200" cy="80" rx="80" ry="20" fill="url(#cylinderGrad)" stroke="white" stroke-width="2"/>
  
  <!-- Cuerpo del cilindro -->
  <rect x="120" y="80" width="160" height="120" fill="url(#cylinderGrad)" stroke="white" stroke-width="2"/>
  
  <!-- Elipse inferior -->
  <ellipse cx="200" cy="200" rx="80" ry="20" fill="url(#cylinderGrad)" stroke="white" stroke-width="2"/>
  
  <!-- Cilindro interno (hueco) -->
  <ellipse cx="200" cy="80" rx="40" ry="10" fill="rgba(255,255,255,0.8)" stroke="white" stroke-width="1"/>
  <rect x="160" y="80" width="80" height="120" fill="rgba(255,255,255,0.8)" stroke="white" stroke-width="1"/>
  <ellipse cx="200" cy="200" rx="40" ry="10" fill="rgba(255,255,255,0.8)" stroke="white" stroke-width="1"/>
  
  <!-- Dimensiones -->
  <line x1="300" y1="80" x2="300" y2="200" stroke="white" stroke-width="2" opacity="0.8"/>
  <text x="310" y="145" font-family="Arial, sans-serif" font-size="12" fill="white">h = ?</text>
  
  <!-- Título -->
  <text x="200" y="35" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="white">
    Cilindro Hueco
  </text>
  <text x="200" y="55" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="rgba(255,255,255,0.9)">
    Formulación y Ejecución
  </text>
  
  <!-- Badge de nivel -->
  <rect x="320" y="15" width="60" height="25" rx="12" fill="rgba(255,255,255,0.2)"/>
  <text x="350" y="32" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">
    Nivel 3
  </text>
  
  <!-- Datos -->
  <text x="200" y="240" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">
    r₁ = 0.3m, r₂ = 0.6m
  </text>
  <text x="200" y="260" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">
    ¿Qué dato falta?
  </text>
</svg>
