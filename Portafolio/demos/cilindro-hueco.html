<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo: <PERSON><PERSON>ndro <PERSON> - Geometría 3D | R-exams ICFES</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .demo-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
        }
        
        .exercise-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: -30px auto 30px;
            max-width: 1000px;
            overflow: hidden;
        }
        
        .exercise-question {
            padding: 40px;
            border-bottom: 3px solid var(--secondary-color);
        }
        
        .cylinder-diagram {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            border: 2px dashed var(--secondary-color);
        }
        
        .dimensions-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .table th {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 15px;
            text-align: center;
        }
        
        .table td {
            padding: 12px 15px;
            text-align: center;
            border-color: #e9ecef;
        }
        
        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option-item:hover {
            border-color: var(--secondary-color);
            background: #e3f2fd;
        }
        
        .option-item.selected {
            border-color: var(--secondary-color);
            background: #e3f2fd;
        }
        
        .option-item.correct {
            border-color: var(--success-color);
            background: #e8f5e8;
        }
        
        .option-item.incorrect {
            border-color: var(--danger-color);
            background: #ffebee;
        }
        
        .solution-panel {
            background: #e8f5e8;
            border-left: 5px solid var(--success-color);
            padding: 25px;
            margin-top: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .formula-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
        }
        
        .tech-badge {
            background: var(--primary-color);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 2px;
            display: inline-block;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, var(--secondary-color), var(--success-color));
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
            color: white;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }
        
        .cylinder-3d {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300"><defs><linearGradient id="cylinderGrad" x1="0%" y1="0%" x2="100%" y2="0%"><stop offset="0%" style="stop-color:%234682B4;stop-opacity:0.8" /><stop offset="50%" style="stop-color:%23EBF5FF;stop-opacity:0.3" /><stop offset="100%" style="stop-color:%234682B4;stop-opacity:0.8" /></linearGradient></defs><ellipse cx="200" cy="50" rx="120" ry="30" fill="url(%23cylinderGrad)" stroke="%234682B4" stroke-width="2"/><rect x="80" y="50" width="240" height="200" fill="url(%23cylinderGrad)" stroke="%234682B4" stroke-width="2"/><ellipse cx="200" cy="250" rx="120" ry="30" fill="url(%23cylinderGrad)" stroke="%234682B4" stroke-width="2"/><ellipse cx="200" cy="50" rx="60" ry="15" fill="white" stroke="%234682B4" stroke-width="2"/><ellipse cx="200" cy="250" rx="60" ry="15" fill="white" stroke="%234682B4" stroke-width="2"/><rect x="140" y="50" width="120" height="200" fill="white" stroke="%234682B4" stroke-width="2"/><text x="350" y="150" font-family="Arial" font-size="14" fill="%234682B4">Altura = ?</text><text x="200" y="280" font-family="Arial" font-size="12" fill="%234682B4" text-anchor="middle">Radio interno = 0.3 m</text><text x="200" y="30" font-family="Arial" font-size="12" fill="%234682B4" text-anchor="middle">Radio externo = 0.6 m</text></svg>') no-repeat center;
            background-size: contain;
            margin: 20px auto;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-cube me-3"></i>Cilindro Hueco - Geometría 3D
                    </h1>
                    <p class="lead mb-4">
                        Ejercicio de Formulación y Ejecución (Nivel 3) - Componente Geométrico-Métrico
                    </p>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="tech-badge">Python</span>
                        <span class="tech-badge">matplotlib</span>
                        <span class="tech-badge">TikZ</span>
                        <span class="tech-badge">Gráficos 3D</span>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="../index.html" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Volver al Portafolio
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Container -->
    <div class="container">
        <div class="exercise-container">
            <!-- Question Section -->
            <div class="exercise-question">
                <h3 class="fw-bold mb-4">Contexto del Problema</h3>
                
                <p class="mb-4">
                    <strong>Camilo</strong> desea saber cuánto <strong>aceite</strong> se necesita para llenar un 
                    <strong>cilindro interno</strong>, pero solamente cuenta con las medidas de las dimensiones 
                    que muestra la figura.
                </p>

                <!-- Cylinder Diagram -->
                <div class="cylinder-diagram">
                    <h5 class="mb-3"><i class="fas fa-shapes me-2"></i>Diagrama del Cilindro Hueco</h5>
                    <div class="cylinder-3d"></div>
                    <p class="text-muted mt-3">
                        <small>Diagrama generado automáticamente con Python/matplotlib</small>
                    </p>
                </div>

                <!-- Dimensions Table -->
                <div class="dimensions-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Dimensión</th>
                                <th>Valor</th>
                                <th>Unidad</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Radio interno</strong></td>
                                <td>0.3</td>
                                <td>m</td>
                            </tr>
                            <tr>
                                <td><strong>Radio externo</strong></td>
                                <td>0.6</td>
                                <td>m</td>
                            </tr>
                            <tr>
                                <td><strong>Diámetro externo</strong></td>
                                <td>1.2</td>
                                <td>m</td>
                            </tr>
                            <tr class="table-warning">
                                <td><strong>Grosor</strong></td>
                                <td>0.3</td>
                                <td>m</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-question-circle me-2"></i>Pregunta</h5>
                    <p class="fw-bold mb-0">
                        ¿Cuál medida le falta a Camilo para hallar la cantidad deseada?
                    </p>
                </div>
            </div>

            <!-- Options Section -->
            <div class="exercise-options p-4">
                <h4 class="fw-bold mb-4">Selecciona la respuesta correcta:</h4>
                
                <div class="option-item" data-option="A">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">A</span>
                        <div>
                            <strong>Radio externo</strong>
                        </div>
                    </div>
                </div>

                <div class="option-item" data-option="B">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">B</span>
                        <div>
                            <strong>Diámetro externo</strong>
                        </div>
                    </div>
                </div>

                <div class="option-item" data-option="C">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">C</span>
                        <div>
                            <strong>Altura del cilindro</strong>
                        </div>
                    </div>
                </div>

                <div class="option-item" data-option="D">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">D</span>
                        <div>
                            <strong>Perímetro del cilindro</strong>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-demo btn-lg" onclick="checkAnswer()">
                        <i class="fas fa-check me-2"></i>Verificar Respuesta
                    </button>
                    <button class="btn btn-outline-secondary btn-lg ms-3" onclick="showSolution()">
                        <i class="fas fa-lightbulb me-2"></i>Ver Solución
                    </button>
                </div>

                <!-- Solution Panel (initially hidden) -->
                <div id="solution-panel" class="solution-panel" style="display: none;">
                    <h5><i class="fas fa-check-circle me-2"></i>Solución Detallada</h5>
                    
                    <h6 class="mt-4 mb-3">Análisis del problema:</h6>
                    
                    <p>Para calcular el volumen de aceite necesario para llenar el cilindro interno, 
                    necesitamos usar la fórmula del volumen de un cilindro:</p>
                    
                    <div class="formula-box">
                        V = π × r² × h
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">✅ Datos disponibles:</h6>
                            <ul>
                                <li>Radio interno (r) = 0.3 m</li>
                                <li>Radio externo = 0.6 m</li>
                                <li>Diámetro externo = 1.2 m</li>
                                <li>Grosor = 0.3 m</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-danger">❌ Dato faltante:</h6>
                            <ul>
                                <li><strong>Altura del cilindro (h)</strong></li>
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-success mt-3">
                        <strong>Respuesta correcta: C - Altura del cilindro</strong><br>
                        Sin la altura, es imposible calcular el volumen del cilindro interno. 
                        Todos los demás datos están disponibles o se pueden calcular a partir de los datos dados.
                    </div>
                    
                    <h6 class="mt-4 mb-3">Cálculo con altura conocida:</h6>
                    <p>Si tuviéramos la altura, el volumen se calcularía así:</p>
                    <div class="formula-box">
                        V = π × (0.3)² × h = 0.283 × h m³
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="feature-highlight">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-cogs me-3"></i>Características Técnicas Avanzadas
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Gráficos 3D profesionales con Python</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Aleatorización física coherente</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Múltiples unidades de medida</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Tablas TikZ dinámicas</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Validación automática de coherencia</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Renderizado optimizado</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <button class="btn btn-outline-light btn-lg" onclick="generateNewDimensions()">
                        <i class="fas fa-sync-alt me-2"></i>Generar Nuevas Dimensiones
                    </button>
                </div>
            </div>
        </div>

        <!-- Code Example -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="fw-bold mb-4">
                    <i class="fas fa-code me-3"></i>Código Python para Gráfico 3D
                </h3>
                <div class="formula-box text-start">
                    <pre><code># Código Python para generar cilindro 3D
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Ellipse, FancyArrowPatch

# Parámetros del cilindro aleatorios
radio_interno = 0.3  # m
radio_externo = 0.6  # m
altura = 1.5  # m (valor aleatorio)

# Crear figura con perspectiva 3D
fig, ax = plt.subplots(figsize=(8, 6))

# Dibujar cilindro hueco con efectos visuales
def dibujar_cilindro_3d():
    # Factores de perspectiva para las elipses
    factor_elipse = 0.35
    vradio_ext = radio_externo * factor_elipse
    vradio_int = radio_interno * factor_elipse
    
    # Crear coordenadas para el cuerpo exterior
    angulos = np.linspace(-np.pi, 0, 50)
    x_ext = radio_externo * np.cos(angulos)
    y_ext = vradio_ext * np.sin(angulos)
    
    # Renderizar con colores y sombras
    ax.plot(x_ext, y_ext, color='#4682B4', linewidth=2)

# Añadir dimensiones y etiquetas profesionales
añadir_cotas_profesionales()

plt.savefig('cilindro_hueco.png', dpi=150)
plt.close()</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedOption = null;
        const correctAnswer = 'C';
        
        // Event listeners para las opciones
        document.querySelectorAll('.option-item').forEach(option => {
            option.addEventListener('click', function() {
                // Remover selección anterior
                document.querySelectorAll('.option-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Seleccionar nueva opción
                this.classList.add('selected');
                selectedOption = this.dataset.option;
            });
        });
        
        function checkAnswer() {
            if (!selectedOption) {
                alert('Por favor, selecciona una opción antes de verificar.');
                return;
            }
            
            document.querySelectorAll('.option-item').forEach(option => {
                const optionLetter = option.dataset.option;
                
                if (optionLetter === correctAnswer) {
                    option.classList.add('correct');
                } else if (optionLetter === selectedOption) {
                    option.classList.add('incorrect');
                }
                
                // Deshabilitar clicks
                option.style.pointerEvents = 'none';
            });
            
            // Mostrar resultado
            const isCorrect = selectedOption === correctAnswer;
            const message = isCorrect ? 
                '¡Correcto! La altura es la dimensión faltante para calcular el volumen.' :
                `Incorrecto. La respuesta correcta es ${correctAnswer}.`;
                
            setTimeout(() => {
                alert(message);
                if (isCorrect) {
                    showSolution();
                }
            }, 500);
        }
        
        function showSolution() {
            document.getElementById('solution-panel').style.display = 'block';
            document.getElementById('solution-panel').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
        
        function generateNewDimensions() {
            // Simular generación de nuevas dimensiones
            const radiosInternos = [0.2, 0.25, 0.3, 0.35, 0.4];
            const factoresRadio = [1.5, 1.8, 2.0, 2.2, 2.5];
            
            const nuevoRadioInterno = radiosInternos[Math.floor(Math.random() * radiosInternos.length)];
            const factor = factoresRadio[Math.floor(Math.random() * factoresRadio.length)];
            const nuevoRadioExterno = (nuevoRadioInterno * factor).toFixed(2);
            const nuevoDiametro = (nuevoRadioExterno * 2).toFixed(2);
            const nuevoGrosor = (nuevoRadioExterno - nuevoRadioInterno).toFixed(2);
            
            alert(`Nuevas dimensiones generadas:\n\nRadio interno: ${nuevoRadioInterno} m\nRadio externo: ${nuevoRadioExterno} m\nDiámetro externo: ${nuevoDiametro} m\nGrosor: ${nuevoGrosor} m\n\nEn una implementación real, esto actualizaría el gráfico 3D automáticamente.`);
        }
    </script>
</body>
</html>
