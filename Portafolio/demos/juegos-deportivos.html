<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo: Juegos Deportivos - Aná<PERSON>is <PERSON>tico | R-exams ICFES</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .demo-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
        }
        
        .exercise-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: -30px auto 30px;
            max-width: 900px;
            overflow: hidden;
        }
        
        .exercise-question {
            padding: 40px;
            border-bottom: 3px solid var(--secondary-color);
        }
        
        .exercise-options {
            padding: 30px 40px;
        }
        
        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option-item:hover {
            border-color: var(--secondary-color);
            background: #e3f2fd;
        }
        
        .option-item.selected {
            border-color: var(--secondary-color);
            background: #e3f2fd;
        }
        
        .option-item.correct {
            border-color: var(--success-color);
            background: #e8f5e8;
        }
        
        .option-item.incorrect {
            border-color: var(--danger-color);
            background: #ffebee;
        }
        
        .data-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .table th {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 15px;
            text-align: center;
        }
        
        .table td {
            padding: 12px 15px;
            text-align: center;
            border-color: #e9ecef;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .table tbody tr:last-child {
            background-color: #fff3cd;
            font-weight: bold;
        }
        
        .solution-panel {
            background: #e8f5e8;
            border-left: 5px solid var(--success-color);
            padding: 25px;
            margin-top: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .code-panel {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, var(--secondary-color), var(--success-color));
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
            color: white;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }
        
        .tech-badge {
            background: var(--primary-color);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-trophy me-3"></i>Juegos Deportivos - Análisis Estadístico
                    </h1>
                    <p class="lead mb-4">
                        Ejercicio de Interpretación y Representación (Nivel 2) - Componente Aleatorio
                    </p>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="tech-badge">R-exams</span>
                        <span class="tech-badge">Python</span>
                        <span class="tech-badge">300+ versiones</span>
                        <span class="tech-badge">Distractores avanzados</span>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="../index.html" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Volver al Portafolio
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Container -->
    <div class="container">
        <div class="exercise-container">
            <!-- Question Section -->
            <div class="exercise-question">
                <h3 class="fw-bold mb-4">Contexto del Problema</h3>
                
                <p class="mb-4">
                    Los <strong>Juegos Panamericanos</strong> se realizan cada cuatro años con la participación de países de 
                    <strong>América</strong>, alternando las sedes deportivas. La tabla muestra algunos datos de las últimas 
                    cinco versiones de los Juegos Panamericanos:
                </p>

                <!-- Data Table -->
                <div class="data-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Año</th>
                                <th>Países</th>
                                <th>Deportes</th>
                                <th>Total de Atletas</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2002</td>
                                <td>42</td>
                                <td>38</td>
                                <td>5.234</td>
                            </tr>
                            <tr>
                                <td>2006</td>
                                <td>45</td>
                                <td>41</td>
                                <td>5.892</td>
                            </tr>
                            <tr>
                                <td>2010</td>
                                <td>48</td>
                                <td>39</td>
                                <td>6.156</td>
                            </tr>
                            <tr>
                                <td>2014</td>
                                <td>41</td>
                                <td>43</td>
                                <td>5.678</td>
                            </tr>
                            <tr class="table-warning">
                                <td><strong>2018</strong></td>
                                <td><strong>47</strong></td>
                                <td><strong>42</strong></td>
                                <td><strong>6.321</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-question-circle me-2"></i>Pregunta</h5>
                    <p class="mb-3">
                        Del total de atletas participantes en <strong>2018</strong>, el <strong>7%</strong> compite en 
                        <strong>natación</strong>. Para determinar el número de atletas de natación ese año, se sugiere 
                        multiplicar <strong>0.7</strong> por el número de atletas que participaron en 2018.
                    </p>
                    <p class="fw-bold mb-0">El procedimiento sugerido es:</p>
                </div>
            </div>

            <!-- Options Section -->
            <div class="exercise-options">
                <h4 class="fw-bold mb-4">Selecciona la respuesta correcta:</h4>
                
                <div class="option-item" data-option="A">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">A</span>
                        <div>
                            <strong>suficiente para determinar el número de atletas que participó en natación en el año 2018</strong>
                        </div>
                    </div>
                </div>

                <div class="option-item" data-option="B">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">B</span>
                        <div>
                            <strong>insuficiente, porque falta multiplicar por el número de países participantes en 2018</strong>
                        </div>
                    </div>
                </div>

                <div class="option-item" data-option="C">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">C</span>
                        <div>
                            <strong>incorrecto, porque se debe multiplicar 0.07 por el número de atletas participantes</strong>
                        </div>
                    </div>
                </div>

                <div class="option-item" data-option="D">
                    <div class="d-flex align-items-start">
                        <span class="badge bg-primary me-3 mt-1">D</span>
                        <div>
                            <strong>insuficiente, porque se debe dividir entre el número de deportes practicados en 2018</strong>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-demo btn-lg" onclick="checkAnswer()">
                        <i class="fas fa-check me-2"></i>Verificar Respuesta
                    </button>
                    <button class="btn btn-outline-secondary btn-lg ms-3" onclick="showSolution()">
                        <i class="fas fa-lightbulb me-2"></i>Ver Solución
                    </button>
                </div>

                <!-- Solution Panel (initially hidden) -->
                <div id="solution-panel" class="solution-panel" style="display: none;">
                    <h5><i class="fas fa-check-circle me-2"></i>Solución Detallada</h5>
                    
                    <h6 class="mt-4 mb-3">Análisis del procedimiento propuesto:</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger">❌ Procedimiento Incorrecto:</h6>
                            <ul>
                                <li>7% = 0.7 ❌</li>
                                <li>0.7 × 6.321 = 4.425 atletas</li>
                                <li>Esto sería el 70%, no el 7%</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">✅ Procedimiento Correcto:</h6>
                            <ul>
                                <li>7% = 0.07 ✅</li>
                                <li>0.07 × 6.321 = 442 atletas</li>
                                <li>Este es el 7% correcto</li>
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-success mt-3">
                        <strong>Respuesta correcta: C</strong><br>
                        El procedimiento es incorrecto porque confunde 0.7 (que representa 70%) con 0.07 (que representa 7%). 
                        Para calcular el 7% se debe usar 0.07, no 0.7.
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="feature-highlight">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-cogs me-3"></i>Características Técnicas Avanzadas
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>300+ versiones únicas automáticas</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>10 contextos deportivos diferentes</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Sistema avanzado de distractores</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Validación matemática robusta</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Integración R-Python</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Compatible con Moodle/PDF/Word</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <button class="btn btn-outline-light btn-lg" onclick="generateNewVersion()">
                        <i class="fas fa-sync-alt me-2"></i>Generar Nueva Versión
                    </button>
                </div>
            </div>
        </div>

        <!-- Code Example -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="fw-bold mb-4">
                    <i class="fas fa-code me-3"></i>Ejemplo de Código R-exams
                </h3>
                <div class="code-panel">
                    <pre><code class="language-r"># Función principal de generación de datos
generar_datos <- function() {
  # Contextos deportivos aleatorios ampliados
  contextos_deportivos <- list(
    list(evento = "Juegos Panamericanos", region = "América"),
    list(evento = "Juegos Olímpicos", region = "mundial"),
    list(evento = "Juegos Centroamericanos", region = "Centroamérica"),
    # ... más contextos
  )
  
  contexto_sel <- sample(contextos_deportivos, 1)[[1]]
  
  # Generar 5 años consecutivos aleatorios
  año_inicial <- sample(1998:2008, 1)
  años <- seq(año_inicial, año_inicial + 16, by = 4)[1:5]
  
  # Sistema avanzado de distractores
  afirmaciones <- generar_distractores_avanzados(
    porcentaje_decimal, atletas_disciplina
  )
  
  return(datos_completos)
}</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        let selectedOption = null;
        const correctAnswer = 'C';
        
        // Event listeners para las opciones
        document.querySelectorAll('.option-item').forEach(option => {
            option.addEventListener('click', function() {
                // Remover selección anterior
                document.querySelectorAll('.option-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Seleccionar nueva opción
                this.classList.add('selected');
                selectedOption = this.dataset.option;
            });
        });
        
        function checkAnswer() {
            if (!selectedOption) {
                alert('Por favor, selecciona una opción antes de verificar.');
                return;
            }
            
            document.querySelectorAll('.option-item').forEach(option => {
                const optionLetter = option.dataset.option;
                
                if (optionLetter === correctAnswer) {
                    option.classList.add('correct');
                } else if (optionLetter === selectedOption) {
                    option.classList.add('incorrect');
                }
                
                // Deshabilitar clicks
                option.style.pointerEvents = 'none';
            });
            
            // Mostrar resultado
            const isCorrect = selectedOption === correctAnswer;
            const message = isCorrect ? 
                '¡Correcto! Has identificado el error en el procedimiento.' :
                `Incorrecto. La respuesta correcta es ${correctAnswer}.`;
                
            setTimeout(() => {
                alert(message);
                if (isCorrect) {
                    showSolution();
                }
            }, 500);
        }
        
        function showSolution() {
            document.getElementById('solution-panel').style.display = 'block';
            document.getElementById('solution-panel').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
        
        function generateNewVersion() {
            // Simular generación de nueva versión
            const eventos = [
                'Juegos Olímpicos', 'Juegos Panamericanos', 'Juegos Centroamericanos',
                'Juegos Suramericanos', 'Juegos Mediterráneos'
            ];
            const disciplinas = [
                'natación', 'atletismo', 'gimnasia', 'ciclismo', 'boxeo'
            ];
            const porcentajes = [5, 6, 7, 8, 9, 10];
            
            const nuevoEvento = eventos[Math.floor(Math.random() * eventos.length)];
            const nuevaDisciplina = disciplinas[Math.floor(Math.random() * disciplinas.length)];
            const nuevoPorcentaje = porcentajes[Math.floor(Math.random() * porcentajes.length)];
            
            alert(`Nueva versión generada:\n\nEvento: ${nuevoEvento}\nDisciplina: ${nuevaDisciplina}\nPorcentaje: ${nuevoPorcentaje}%\n\nEn una implementación real, esto actualizaría toda la pregunta automáticamente.`);
        }
    </script>
</body>
</html>
