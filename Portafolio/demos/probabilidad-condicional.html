<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo: Probabilidad Condicional - Razonamiento Avanzado | R-exams ICFES</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --purple-color: #9b59b6;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .demo-header {
            background: linear-gradient(135deg, var(--purple-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
        }
        
        .exercise-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: -30px auto 30px;
            max-width: 1000px;
            overflow: hidden;
        }
        
        .exercise-question {
            padding: 40px;
            border-bottom: 3px solid var(--purple-color);
        }
        
        .contingency-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
            border: 2px solid var(--purple-color);
        }
        
        .table th {
            background: var(--purple-color);
            color: white;
            border: none;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .table td {
            padding: 15px;
            text-align: center;
            border-color: #e9ecef;
            font-weight: 500;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option-item:hover {
            border-color: var(--purple-color);
            background: #f3e5f5;
        }
        
        .option-item.selected {
            border-color: var(--purple-color);
            background: #f3e5f5;
        }
        
        .option-item.correct {
            border-color: var(--success-color);
            background: #e8f5e8;
        }
        
        .option-item.incorrect {
            border-color: var(--danger-color);
            background: #ffebee;
        }
        
        .solution-panel {
            background: #e8f5e8;
            border-left: 5px solid var(--success-color);
            padding: 25px;
            margin-top: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .formula-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
        }
        
        .probability-calculation {
            background: #fff3cd;
            border: 2px solid var(--warning-color);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .tech-badge {
            background: var(--purple-color);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 2px;
            display: inline-block;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, var(--purple-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(155, 89, 182, 0.3);
            color: white;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }
        
        .fraction {
            display: inline-block;
            text-align: center;
            vertical-align: middle;
        }
        
        .fraction .numerator {
            display: block;
            border-bottom: 1px solid;
            padding-bottom: 2px;
        }
        
        .fraction .denominator {
            display: block;
            padding-top: 2px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="demo-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-chart-pie me-3"></i>Probabilidad Condicional - Razonamiento Avanzado
                    </h1>
                    <p class="lead mb-4">
                        Ejercicio de Razonamiento (Nivel 3) - Componente Aleatorio
                    </p>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="tech-badge">R-exams</span>
                        <span class="tech-badge">TikZ</span>
                        <span class="tech-badge">testthat</span>
                        <span class="tech-badge">Matemáticas avanzadas</span>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="../index.html" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Volver al Portafolio
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Container -->
    <div class="container">
        <div class="exercise-container">
            <!-- Question Section -->
            <div class="exercise-question">
                <h3 class="fw-bold mb-4">Contexto del Problema</h3>
                
                <p class="mb-4">
                    En un <strong>taller de verano</strong> se registraron los siguientes datos estadísticos 
                    sobre los <strong>participantes</strong> según su género y grupo de edad:
                </p>

                <!-- Contingency Table -->
                <div class="contingency-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Grupo de edad</th>
                                <th>Hombres</th>
                                <th>Mujeres</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Menores de 18 años</strong></td>
                                <td>0.234</td>
                                <td>0.186</td>
                            </tr>
                            <tr>
                                <td><strong>Mayores de 18 años</strong></td>
                                <td>0.312</td>
                                <td>0.268</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-question-circle me-2"></i>Pregunta</h5>
                    <p class="mb-3">
                        Si se selecciona aleatoriamente una <strong>mujer</strong> del taller, 
                        ¿cuál es la probabilidad de que sea <strong>mayor de 18 años</strong>?
                    </p>
                    <p class="fw-bold mb-0">
                        Expresar la respuesta como fracción usando los valores de la tabla.
                    </p>
                </div>
            </div>

            <!-- Options Section -->
            <div class="exercise-options p-4">
                <h4 class="fw-bold mb-4">Selecciona la respuesta correcta:</h4>
                
                <div class="option-item" data-option="A">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-start">
                            <span class="badge bg-primary me-3 mt-1">A</span>
                            <div>
                                <span class="fraction">
                                    <span class="numerator">0.268</span>
                                    <span class="denominator">0.454</span>
                                </span>
                            </div>
                        </div>
                        <small class="text-muted">P(Mayor|Mujer)</small>
                    </div>
                </div>

                <div class="option-item" data-option="B">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-start">
                            <span class="badge bg-primary me-3 mt-1">B</span>
                            <div>
                                <span class="fraction">
                                    <span class="numerator">0.268</span>
                                    <span class="denominator">0.580</span>
                                </span>
                            </div>
                        </div>
                        <small class="text-muted">Distractor: P(Mayor∩Mujer)/P(Mayor)</small>
                    </div>
                </div>

                <div class="option-item" data-option="C">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-start">
                            <span class="badge bg-primary me-3 mt-1">C</span>
                            <div>
                                <span class="fraction">
                                    <span class="numerator">0.454</span>
                                    <span class="denominator">0.268</span>
                                </span>
                            </div>
                        </div>
                        <small class="text-muted">Distractor: P(Mujer)/P(Mayor∩Mujer)</small>
                    </div>
                </div>

                <div class="option-item" data-option="D">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-start">
                            <span class="badge bg-primary me-3 mt-1">D</span>
                            <div>
                                <span class="fraction">
                                    <span class="numerator">0.186</span>
                                    <span class="denominator">0.454</span>
                                </span>
                            </div>
                        </div>
                        <small class="text-muted">Distractor: P(Menor∩Mujer)/P(Mujer)</small>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-demo btn-lg" onclick="checkAnswer()">
                        <i class="fas fa-check me-2"></i>Verificar Respuesta
                    </button>
                    <button class="btn btn-outline-secondary btn-lg ms-3" onclick="showSolution()">
                        <i class="fas fa-lightbulb me-2"></i>Ver Solución
                    </button>
                </div>

                <!-- Solution Panel (initially hidden) -->
                <div id="solution-panel" class="solution-panel" style="display: none;">
                    <h5><i class="fas fa-check-circle me-2"></i>Solución Detallada</h5>
                    
                    <h6 class="mt-4 mb-3">Análisis de probabilidad condicional:</h6>
                    
                    <p>Necesitamos calcular P(Mayor de 18 años | Mujer), que se lee como 
                    "la probabilidad de que sea mayor de 18 años, dado que es mujer".</p>
                    
                    <div class="formula-box">
                        P(A|B) = P(A ∩ B) / P(B)
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="probability-calculation">
                                <h6 class="text-primary">📊 Datos de la tabla:</h6>
                                <ul class="mb-0">
                                    <li>P(Mayor ∩ Mujer) = 0.268</li>
                                    <li>P(Menor ∩ Mujer) = 0.186</li>
                                    <li>P(Mujer) = 0.268 + 0.186 = 0.454</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="probability-calculation">
                                <h6 class="text-success">✅ Cálculo correcto:</h6>
                                <p class="mb-0">
                                    P(Mayor|Mujer) = <span class="fraction">
                                        <span class="numerator">0.268</span>
                                        <span class="denominator">0.454</span>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success mt-3">
                        <strong>Respuesta correcta: A</strong><br>
                        La probabilidad condicional se calcula dividiendo la probabilidad de la intersección 
                        (Mayor ∩ Mujer = 0.268) entre la probabilidad de la condición (Mujer = 0.454).
                    </div>
                    
                    <h6 class="mt-4 mb-3">Análisis de distractores:</h6>
                    <ul>
                        <li><strong>Opción B:</strong> Confunde P(A|B) con P(B|A)</li>
                        <li><strong>Opción C:</strong> Invierte numerador y denominador</li>
                        <li><strong>Opción D:</strong> Usa la probabilidad del evento complementario</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="feature-highlight">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-cogs me-3"></i>Características Técnicas Avanzadas
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>8 tipos de preguntas diferentes</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Generación matemáticamente coherente</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Distractores sofisticados</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Validación estricta con testthat</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>12 contextos educativos</li>
                                <li class="mb-2"><i class="fas fa-check text-warning me-2"></i>Tablas TikZ profesionales</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <button class="btn btn-outline-light btn-lg" onclick="generateNewScenario()">
                        <i class="fas fa-sync-alt me-2"></i>Generar Nuevo Escenario
                    </button>
                </div>
            </div>
        </div>

        <!-- Code Example -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="fw-bold mb-4">
                    <i class="fas fa-code me-3"></i>Código R para Generación Coherente
                </h3>
                <div class="formula-box text-start">
                    <pre><code># Función para generar proporciones coherentes
generar_proporciones_coherentes <- function() {
  repeat {
    # Generar valores base con distribuciones realistas
    tipo_distribucion <- sample(1:5, 1)
    
    if (tipo_distribucion == 1) {
      # Distribución equilibrada
      p11_base <- sample(18:28, 1)   # Menores masculino
      p12_base <- sample(15:25, 1)   # Menores femenino  
      p21_base <- sample(25:35, 1)   # Mayores masculino
      p22_base <- sample(20:30, 1)   # Mayores femenino
    }
    
    # Normalización matemática exacta
    total_base <- p11_base + p12_base + p21_base + p22_base
    
    # Convertir a proporciones con 3 decimales
    p11 <- round(p11_base / total_base, 3)
    p12 <- round(p12_base / total_base, 3)
    p21 <- round(p21_base / total_base, 3)
    p22 <- round(p22_base / total_base, 3)
    
    # Validación estricta de coherencia matemática
    suma_final <- p11 + p12 + p21 + p22
    
    if (abs(suma_final - 1.0) <= 0.001 &&
        all(proporciones >= 0.05) &&
        all(marginales >= 0.20)) {
      return(c(p11, p12, p21, p22))
    }
  }
}</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedOption = null;
        const correctAnswer = 'A';
        
        // Event listeners para las opciones
        document.querySelectorAll('.option-item').forEach(option => {
            option.addEventListener('click', function() {
                // Remover selección anterior
                document.querySelectorAll('.option-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Seleccionar nueva opción
                this.classList.add('selected');
                selectedOption = this.dataset.option;
            });
        });
        
        function checkAnswer() {
            if (!selectedOption) {
                alert('Por favor, selecciona una opción antes de verificar.');
                return;
            }
            
            document.querySelectorAll('.option-item').forEach(option => {
                const optionLetter = option.dataset.option;
                
                if (optionLetter === correctAnswer) {
                    option.classList.add('correct');
                } else if (optionLetter === selectedOption) {
                    option.classList.add('incorrect');
                }
                
                // Deshabilitar clicks
                option.style.pointerEvents = 'none';
            });
            
            // Mostrar resultado
            const isCorrect = selectedOption === correctAnswer;
            const message = isCorrect ? 
                '¡Correcto! Has aplicado correctamente la fórmula de probabilidad condicional.' :
                `Incorrecto. La respuesta correcta es ${correctAnswer}.`;
                
            setTimeout(() => {
                alert(message);
                if (isCorrect) {
                    showSolution();
                }
            }, 500);
        }
        
        function showSolution() {
            document.getElementById('solution-panel').style.display = 'block';
            document.getElementById('solution-panel').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
        
        function generateNewScenario() {
            // Simular generación de nuevo escenario
            const contextos = [
                'curso vacacional', 'seminario intensivo', 'programa de capacitación',
                'workshop educativo', 'entrenamiento técnico'
            ];
            const edades = [16, 17, 18, 19, 20, 21];
            const terminos = [
                ['hombres', 'mujeres'],
                ['varones', 'participantes de género femenino'],
                ['participantes masculinos', 'estudiantes de género femenino']
            ];
            
            const nuevoContexto = contextos[Math.floor(Math.random() * contextos.length)];
            const nuevaEdad = edades[Math.floor(Math.random() * edades.length)];
            const nuevosTerminos = terminos[Math.floor(Math.random() * terminos.length)];
            
            // Generar nuevas proporciones
            const p1 = (Math.random() * 0.2 + 0.15).toFixed(3);
            const p2 = (Math.random() * 0.2 + 0.15).toFixed(3);
            const p3 = (Math.random() * 0.2 + 0.2).toFixed(3);
            const p4 = (1 - p1 - p2 - p3).toFixed(3);
            
            alert(`Nuevo escenario generado:\n\nContexto: ${nuevoContexto}\nEdad de corte: ${nuevaEdad} años\nTérminos: ${nuevosTerminos[0]} / ${nuevosTerminos[1]}\n\nNuevas proporciones:\nMenores ${nuevosTerminos[0]}: ${p1}\nMenores ${nuevosTerminos[1]}: ${p2}\nMayores ${nuevosTerminos[0]}: ${p3}\nMayores ${nuevosTerminos[1]}: ${p4}\n\nEn una implementación real, esto actualizaría toda la tabla automáticamente.`);
        }
    </script>
</body>
</html>
