// Datos de los ejercicios demo para el portafolio
const ejerciciosDemo = [
    {
        id: 1,
        titulo: "Juegos Deportivos - Análisis <PERSON>ístico",
        descripcion: "Ejercicio avanzado de interpretación y representación estadística con contexto deportivo realista. Evalúa la capacidad de identificar errores en procedimientos de cálculo de porcentajes.",
        competencia: "interpretacion_representacion",
        competenciaTexto: "Interpretación y Representación",
        nivel: 2,
        categoria: "Estadística",
        contexto: "Comunitario - Eventos Deportivos",
        archivo: "juegos_deportivos_aleatorio_interpretacion_representacion_n2_opcA_v1.Rmd",
        caracteristicas: [
            "300+ versiones únicas verificadas automáticamente",
            "Sistema avanzado de distractores pedagógicos",
            "Integración R-Python para tablas dinámicas",
            "Contextos deportivos aleatorizados (10 eventos diferentes)",
            "Validación matemática robusta con testthat",
            "Formato compatible con Moodle, PDF y Word"
        ],
        tecnologias: ["R-exams", "Python", "reticulate", "testthat", "matplotlib"],
        codigoEjemplo: `# Función principal de generación de datos
generar_datos <- function() {
  # Contextos deportivos aleatorios ampliados
  contextos_deportivos <- list(
    list(evento = "Juegos Panamericanos", region = "América"),
    list(evento = "Juegos Olímpicos", region = "mundial"),
    # ... más contextos
  )
  
  contexto_sel <- sample(contextos_deportivos, 1)[[1]]
  
  # Generar 5 años consecutivos aleatorios
  año_inicial <- sample(1998:2008, 1)
  años <- seq(año_inicial, año_inicial + 16, by = 4)[1:5]
  
  # Sistema avanzado de distractores
  afirmaciones <- generar_distractores_avanzados(
    porcentaje_decimal, atletas_disciplina
  )
  
  return(datos_completos)
}`,
        imagen: "images/ejercicio-deportes.svg",
        demoUrl: "demos/juegos-deportivos.html",
        githubUrl: "https://github.com/tu-usuario/proyecto-r-exams/blob/main/Lab/15-S1-2025-SEDQ/"
    },
    {
        id: 2,
        titulo: "Cilindro Hueco - Geometría 3D",
        descripcion: "Problema de formulación y ejecución geométrica con visualización 3D avanzada. Determina qué información falta para calcular el volumen de líquido en un cilindro hueco.",
        competencia: "formulacion_ejecucion",
        competenciaTexto: "Formulación y Ejecución",
        nivel: 3,
        categoria: "Geometría",
        contexto: "Matemático - Aplicación Práctica",
        archivo: "volumen_cilindro_hueco_py_v1.Rmd",
        caracteristicas: [
            "Gráficos 3D profesionales con Python/matplotlib",
            "Aleatorización compleja de dimensiones físicamente coherentes",
            "Tablas TikZ con colores y estilos aleatorios",
            "Validación de coherencia física automática",
            "Múltiples unidades de medida (m, cm, dm)",
            "Renderizado optimizado para diferentes formatos"
        ],
        tecnologias: ["Python", "matplotlib", "TikZ", "LaTeX", "R-exams"],
        codigoEjemplo: `# Código Python para generar cilindro 3D
codigo_python <- paste0("
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Ellipse, FancyArrowPatch

# Parámetros del cilindro aleatorios
radio_interno = ", r_int, "
radio_externo = ", r_ext, "
altura = ", altura, "

# Crear figura con perspectiva 3D
fig, ax = plt.subplots(figsize=(8, 6))

# Dibujar cilindro hueco con efectos visuales
dibujar_cilindro_3d(radio_interno, radio_externo, altura)

# Añadir dimensiones y etiquetas
añadir_cotas_profesionales()

plt.savefig('cilindro_hueco.png', dpi=150)
")

py_run_string(codigo_python)`,
        imagen: "images/ejercicio-cilindro.svg",
        demoUrl: "demos/cilindro-hueco.html",
        githubUrl: "https://github.com/tu-usuario/proyecto-r-exams/blob/main/Lab/36/"
    },
    {
        id: 3,
        titulo: "Probabilidad Condicional - Razonamiento Avanzado",
        descripcion: "Ejercicio de razonamiento matemático con tablas de contingencia. Evalúa comprensión profunda de probabilidad condicional en contextos educativos.",
        competencia: "razonamiento",
        competenciaTexto: "Razonamiento",
        nivel: 3,
        categoria: "Estadística",
        contexto: "Educativo - Análisis de Datos",
        archivo: "probabilidad_condicional_tabla_contingencia_razonamiento_nivel3_v2.Rmd",
        caracteristicas: [
            "Generación matemáticamente coherente de proporciones",
            "8 tipos diferentes de preguntas de probabilidad condicional",
            "Distractores sofisticados que eliminan patrones detectables",
            "Validación estricta de coherencia matemática",
            "Aleatorización de contextos educativos (12 opciones)",
            "Tablas TikZ con paletas de colores profesionales"
        ],
        tecnologias: ["R", "TikZ", "testthat", "probabilidad", "LaTeX"],
        codigoEjemplo: `# Función para generar proporciones coherentes
generar_proporciones_coherentes <- function() {
  repeat {
    # Generar valores base con distribuciones realistas
    tipo_distribucion <- sample(1:5, 1)
    
    # Normalización matemática exacta
    total_base <- p11_base + p12_base + p21_base + p22_base
    
    # Validación estricta de coherencia
    if (abs(suma_final - 1.0) <= 0.001 &&
        all(proporciones >= 0.05) &&
        all(marginales >= 0.20)) {
      return(proporciones_validadas)
    }
  }
}

# Sistema avanzado de distractores
generar_distractores_desafiantes <- function() {
  # Estrategia 1: Mismo numerador, denominador diferente
  # Estrategia 2: Mismo denominador, numerador diferente  
  # Estrategia 3: Intercambio P(A|B) <-> P(B|A)
  return(distractores_pedagogicos)
}`,
        imagen: "images/ejercicio-probabilidad.svg",
        demoUrl: "demos/probabilidad-condicional.html",
        githubUrl: "https://github.com/tu-usuario/proyecto-r-exams/blob/main/Lab/02-S1-2025-SEDQ/"
    },
    {
        id: 4,
        titulo: "Parabrisas Geométrico - Interpretación Visual",
        descripcion: "Problema de interpretación geométrica con múltiples opciones visuales. Evalúa comprensión de ángulos y dimensiones en contextos aplicados.",
        competencia: "interpretacion_representacion",
        competenciaTexto: "Interpretación y Representación",
        nivel: 2,
        categoria: "Geometría",
        contexto: "Aplicado - Diseño Industrial",
        archivo: "parabrisas.Rmd",
        caracteristicas: [
            "4 opciones visuales generadas dinámicamente con Python",
            "Aleatorización de dimensiones y contextos",
            "Gráficos vectoriales de alta calidad",
            "Contexto aplicado realista (diseño automotriz)",
            "Validación automática de opciones correctas",
            "Optimización para diferentes formatos de salida"
        ],
        tecnologias: ["Python", "matplotlib", "numpy", "R-exams"],
        codigoEjemplo: `# Función para generar esquemas de opciones
generar_esquema_python <- function(opcion, letra) {
  codigo <- '
import matplotlib.pyplot as plt
import numpy as np

fig, ax = plt.subplots(figsize=(8, 4))

def dibujar_abanico(x_origin, y_origin, radius):
    # Arco con ángulo de 90 grados
    theta = np.linspace(0, np.pi/2, 100)
    ax.plot(x_origin + radius * np.cos(theta), 
            y_origin + radius * np.sin(theta),
            color="black", linewidth=2)
    
    # Añadir símbolo de ángulo recto
    añadir_simbolo_90_grados(x_origin, y_origin)

# Generar múltiples opciones con variaciones
dibujar_abanico(1, 0, 2.5)
dibujar_abanico(3.75, 0, 2.5)

# Añadir dimensiones específicas para cada opción
añadir_dimensiones_aleatorias()

plt.savefig(f"opcion{letra}.png", dpi=150)
'
  return(codigo)
}`,
        imagen: "images/ejercicio-parabrisas.svg",
        demoUrl: "demos/parabrisas-geometrico.html",
        githubUrl: "https://github.com/tu-usuario/proyecto-r-exams/blob/main/Lab/39/"
    },
    {
        id: 5,
        titulo: "Descuentos y Porcentajes - Aplicación Comercial",
        descripcion: "Ejercicio de formulación y ejecución con contexto comercial realista. Evalúa habilidades de cálculo de descuentos y análisis financiero básico.",
        competencia: "formulacion_ejecucion",
        competenciaTexto: "Formulación y Ejecución",
        nivel: 2,
        categoria: "Álgebra",
        contexto: "Laboral - Comercio",
        archivo: "descuentos_porcentajes_v2.Rmd",
        caracteristicas: [
            "Contextos comerciales realistas y variados",
            "Visualizaciones de fórmulas matemáticas",
            "Cálculos de descuentos escalonados",
            "Validación de coherencia financiera",
            "Múltiples escenarios de aplicación",
            "Integración con casos de uso reales"
        ],
        tecnologias: ["R", "ggplot2", "LaTeX", "matemáticas financieras"],
        codigoEjemplo: `# Generación de escenarios comerciales
generar_escenario_comercial <- function() {
  # Productos y contextos aleatorios
  productos <- c("electrodomésticos", "ropa", "tecnología", 
                "muebles", "deportes", "libros")
  producto <- sample(productos, 1)
  
  # Precios y descuentos realistas
  precio_original <- sample(100:2000, 1)
  descuento_porcentaje <- sample(10:50, 1)
  
  # Cálculo del precio final
  precio_descuento <- precio_original * (descuento_porcentaje/100)
  precio_final <- precio_original - precio_descuento
  
  # Generar distractores basados en errores comunes
  distractores <- generar_errores_comunes(
    precio_original, descuento_porcentaje
  )
  
  return(escenario_completo)
}`,
        imagen: "images/ejercicio-descuentos.svg",
        demoUrl: "demos/descuentos-porcentajes.html",
        githubUrl: "https://github.com/tu-usuario/proyecto-r-exams/blob/main/Lab/09-S2-2025-SEDQ/"
    }
];

// Función para obtener el color de la competencia
function getCompetenciaColor(competencia) {
    const colores = {
        'interpretacion_representacion': '#27ae60',
        'formulacion_ejecucion': '#2196f3', 
        'razonamiento': '#ff9800'
    };
    return colores[competencia] || '#6c757d';
}

// Función para obtener la clase CSS de la competencia
function getCompetenciaClass(competencia) {
    const clases = {
        'interpretacion_representacion': 'badge-interpretacion',
        'formulacion_ejecucion': 'badge-formulacion',
        'razonamiento': 'badge-razonamiento'
    };
    return clases[competencia] || 'badge-secondary';
}

// Función para generar indicador de dificultad
function generateDifficultyIndicator(nivel) {
    let html = '<div class="difficulty-indicator">';
    html += '<span class="me-2">Nivel:</span>';
    for (let i = 1; i <= 4; i++) {
        const activeClass = i <= nivel ? 'active' : '';
        html += `<div class="difficulty-dot ${activeClass}"></div>`;
    }
    html += '</div>';
    return html;
}

// Exportar para uso global
window.ejerciciosDemo = ejerciciosDemo;
window.getCompetenciaColor = getCompetenciaColor;
window.getCompetenciaClass = getCompetenciaClass;
window.generateDifficultyIndicator = generateDifficultyIndicator;
