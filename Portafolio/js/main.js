// JavaScript principal para el portafolio R-exams ICFES

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes
    initNavbar();
    initSmoothScrolling();
    initExerciseCards();
    initContactForm();
    initAnimations();
});

// Configuración de la navbar con efecto scroll
function initNavbar() {
    const navbar = document.querySelector('.navbar-custom');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// Smooth scrolling para enlaces de navegación
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 80; // Ajuste para navbar fija
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Generar y mostrar tarjetas de ejercicios
function initExerciseCards() {
    const container = document.getElementById('ejercicios-container');
    if (!container) return;

    let html = '';
    
    ejerciciosDemo.forEach((ejercicio, index) => {
        html += generateExerciseCard(ejercicio, index);
    });
    
    container.innerHTML = html;
    
    // Añadir event listeners para los botones
    addExerciseEventListeners();
}

// Generar HTML para una tarjeta de ejercicio
function generateExerciseCard(ejercicio, index) {
    const competenciaClass = getCompetenciaClass(ejercicio.competencia);
    const difficultyIndicator = generateDifficultyIndicator(ejercicio.nivel);
    
    return `
        <div class="exercise-card" data-exercise-id="${ejercicio.id}">
            <div class="exercise-header">
                <div class="row align-items-start">
                    <div class="col-lg-8">
                        <h4 class="fw-bold mb-3">${ejercicio.titulo}</h4>
                        <p class="text-muted mb-3">${ejercicio.descripcion}</p>
                        
                        <div class="mb-3">
                            <span class="competency-badge ${competenciaClass}">
                                ${ejercicio.competenciaTexto}
                            </span>
                            <span class="competency-badge badge-secondary">
                                ${ejercicio.categoria}
                            </span>
                            <span class="competency-badge badge-info">
                                ${ejercicio.contexto}
                            </span>
                        </div>
                        
                        ${difficultyIndicator}
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <div class="mb-3">
                            <img src="${ejercicio.imagen}" alt="${ejercicio.titulo}" 
                                 class="img-fluid rounded shadow-sm" style="max-height: 120px;">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="exercise-body p-4">
                <div class="row">
                    <div class="col-lg-6">
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-star text-warning me-2"></i>Características Destacadas
                        </h6>
                        <ul class="list-unstyled">
                            ${ejercicio.caracteristicas.map(item => 
                                `<li class="mb-2"><i class="fas fa-check text-success me-2"></i>${item}</li>`
                            ).join('')}
                        </ul>
                    </div>
                    <div class="col-lg-6">
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-code text-primary me-2"></i>Tecnologías Utilizadas
                        </h6>
                        <div class="tech-stack mb-4">
                            ${ejercicio.tecnologias.map(tech => 
                                `<span class="tech-badge">${tech}</span>`
                            ).join('')}
                        </div>
                        
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-primary btn-sm" 
                                    onclick="showCodeExample(${ejercicio.id})">
                                <i class="fas fa-code me-1"></i>Ver Código
                            </button>
                            <a href="${ejercicio.demoUrl}" class="btn btn-primary btn-sm" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>Demo Interactivo
                            </a>
                            <a href="${ejercicio.githubUrl}" class="btn btn-outline-dark btn-sm" target="_blank">
                                <i class="fab fa-github me-1"></i>GitHub
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Código de ejemplo (inicialmente oculto) -->
                <div id="code-example-${ejercicio.id}" class="code-example mt-4" style="display: none;">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-terminal text-success me-2"></i>Ejemplo de Código
                    </h6>
                    <pre><code class="language-r">${ejercicio.codigoEjemplo}</code></pre>
                </div>
            </div>
        </div>
    `;
}

// Añadir event listeners para los ejercicios
function addExerciseEventListeners() {
    // Los event listeners se añaden mediante onclick en el HTML
    // para evitar problemas de timing con el DOM
}

// Mostrar/ocultar ejemplo de código
function showCodeExample(exerciseId) {
    const codeElement = document.getElementById(`code-example-${exerciseId}`);
    if (codeElement) {
        if (codeElement.style.display === 'none') {
            codeElement.style.display = 'block';
            // Re-highlight syntax
            if (window.Prism) {
                Prism.highlightAllUnder(codeElement);
            }
        } else {
            codeElement.style.display = 'none';
        }
    }
}

// Configurar formulario de contacto
function initContactForm() {
    const form = document.getElementById('contact-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Obtener datos del formulario
        const formData = {
            nombre: document.getElementById('nombre').value,
            email: document.getElementById('email').value,
            institucion: document.getElementById('institucion').value,
            presupuesto: document.getElementById('presupuesto').value,
            proyecto: document.getElementById('proyecto').value
        };
        
        // Validar campos requeridos
        if (!formData.nombre || !formData.email || !formData.proyecto) {
            showAlert('Por favor, completa todos los campos requeridos.', 'warning');
            return;
        }
        
        // Simular envío (en producción, conectar con backend)
        showAlert('¡Mensaje enviado correctamente! Te contactaré pronto.', 'success');
        form.reset();
    });
}

// Mostrar alertas
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove después de 5 segundos
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Inicializar animaciones
function initAnimations() {
    // Animación de números en las estadísticas
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStatNumbers();
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    const statsSection = document.getElementById('estadisticas');
    if (statsSection) {
        observer.observe(statsSection);
    }
    
    // Animación de aparición de tarjetas
    initCardAnimations();
}

// Animar números de estadísticas
function animateStatNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(element => {
        const finalValue = element.textContent;
        const isPercentage = finalValue.includes('%');
        const isPlusSign = finalValue.includes('+');
        const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
        
        let currentValue = 0;
        const increment = Math.ceil(numericValue / 50);
        const duration = 2000; // 2 segundos
        const stepTime = duration / (numericValue / increment);
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= numericValue) {
                currentValue = numericValue;
                clearInterval(timer);
            }
            
            let displayValue = currentValue.toString();
            if (isPlusSign) displayValue += '+';
            if (isPercentage) displayValue += '%';
            
            element.textContent = displayValue;
        }, stepTime);
    });
}

// Animaciones de tarjetas
function initCardAnimations() {
    const cards = document.querySelectorAll('.feature-card, .exercise-card');
    
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                cardObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });
}

// Función global para mostrar ejemplos de código
window.showCodeExample = showCodeExample;

// Función para filtrar ejercicios por competencia
function filterExercises(competencia) {
    const cards = document.querySelectorAll('.exercise-card');
    
    cards.forEach(card => {
        const exerciseId = parseInt(card.dataset.exerciseId);
        const exercise = ejerciciosDemo.find(e => e.id === exerciseId);
        
        if (!competencia || exercise.competencia === competencia) {
            card.style.display = 'block';
            card.style.animation = 'fadeIn 0.5s ease';
        } else {
            card.style.display = 'none';
        }
    });
}

// Función para buscar ejercicios
function searchExercises(query) {
    const cards = document.querySelectorAll('.exercise-card');
    const searchTerm = query.toLowerCase();
    
    cards.forEach(card => {
        const exerciseId = parseInt(card.dataset.exerciseId);
        const exercise = ejerciciosDemo.find(e => e.id === exerciseId);
        
        const searchableText = [
            exercise.titulo,
            exercise.descripcion,
            exercise.categoria,
            exercise.competenciaTexto,
            ...exercise.caracteristicas,
            ...exercise.tecnologias
        ].join(' ').toLowerCase();
        
        if (searchableText.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Exportar funciones para uso global
window.filterExercises = filterExercises;
window.searchExercises = searchExercises;
