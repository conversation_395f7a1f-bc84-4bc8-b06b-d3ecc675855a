/* Estilos personalizados adicionales para el portafolio R-exams ICFES */

/* Variables CSS personalizadas */
:root {
    --primary-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    --success-gradient: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    --warning-gradient: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    --danger-gradient: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    --purple-gradient: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
    
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    
    --transition-fast: all 0.2s ease;
    --transition-medium: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Animaciones personalizadas */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Clases de utilidad para animaciones */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s;
}

/* Efectos hover mejorados */
.hover-lift {
    transition: var(--transition-medium);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.hover-scale {
    transition: var(--transition-medium);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: var(--transition-medium);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(52, 152, 219, 0.4);
}

/* Gradientes de fondo personalizados */
.bg-gradient-primary {
    background: var(--primary-gradient);
}

.bg-gradient-success {
    background: var(--success-gradient);
}

.bg-gradient-warning {
    background: var(--warning-gradient);
}

.bg-gradient-danger {
    background: var(--danger-gradient);
}

.bg-gradient-purple {
    background: var(--purple-gradient);
}

/* Botones personalizados mejorados */
.btn-gradient-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: var(--border-radius-lg);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.btn-gradient-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-medium);
}

.btn-gradient-primary:hover::before {
    left: 100%;
}

.btn-gradient-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
    color: white;
}

/* Tarjetas mejoradas */
.card-modern {
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-light);
    border: none;
    transition: var(--transition-medium);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-modern .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 3px solid var(--bs-primary);
    padding: 25px;
}

.card-modern .card-body {
    padding: 30px;
}

/* Badges mejorados */
.badge-modern {
    padding: 8px 16px;
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.badge-interpretacion {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.badge-formulacion {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #0d47a1;
    border: 1px solid #90caf9;
}

.badge-razonamiento {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #e65100;
    border: 1px solid #ffcc02;
}

/* Indicadores de progreso personalizados */
.progress-modern {
    height: 8px;
    border-radius: var(--border-radius-sm);
    background: #e9ecef;
    overflow: hidden;
}

.progress-modern .progress-bar {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
    transition: width 1s ease-in-out;
}

/* Tooltips personalizados */
.tooltip-modern {
    position: relative;
    cursor: help;
}

.tooltip-modern::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-medium);
    z-index: 1000;
}

.tooltip-modern:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Secciones con patrones de fondo */
.section-pattern {
    position: relative;
    overflow: hidden;
}

.section-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(46, 204, 113, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* Efectos de texto */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Líneas divisorias decorativas */
.divider-modern {
    height: 3px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
    margin: 30px 0;
    position: relative;
}

.divider-modern::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: white;
    border: 3px solid #3498db;
    border-radius: 50%;
}

/* Iconos con efectos */
.icon-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    font-size: 1.5rem;
    transition: var(--transition-medium);
}

.icon-modern:hover {
    transform: rotate(360deg) scale(1.1);
}

/* Responsive mejoras */
@media (max-width: 768px) {
    .card-modern .card-header,
    .card-modern .card-body {
        padding: 20px;
    }
    
    .btn-gradient-primary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .icon-modern {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Estados de carga */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mejoras de accesibilidad */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus states mejorados */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
    border-color: #3498db;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card-modern {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .btn {
        border: 1px solid #ddd;
    }
}
