<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Imágenes - Portafolio R-exams</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .image-container {
            display: inline-block;
            margin: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .image-container img {
            max-width: 300px;
            height: auto;
            border-radius: 5px;
        }
        .image-title {
            margin-top: 10px;
            font-weight: bold;
            color: #333;
        }
        .status {
            margin-top: 5px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🖼️ Test de Imágenes del Portafolio</h1>
    <p>Esta página verifica que todas las imágenes se carguen correctamente.</p>
    
    <div class="image-container">
        <img src="images/ejercicio-deportes.svg" alt="Ejercicio Deportes" onload="showStatus(this, true)" onerror="showStatus(this, false)">
        <div class="image-title">Juegos Deportivos</div>
        <div class="status" id="status-deportes">Cargando...</div>
    </div>
    
    <div class="image-container">
        <img src="images/ejercicio-cilindro.svg" alt="Ejercicio Cilindro" onload="showStatus(this, true)" onerror="showStatus(this, false)">
        <div class="image-title">Cilindro Hueco</div>
        <div class="status" id="status-cilindro">Cargando...</div>
    </div>
    
    <div class="image-container">
        <img src="images/ejercicio-probabilidad.svg" alt="Ejercicio Probabilidad" onload="showStatus(this, true)" onerror="showStatus(this, false)">
        <div class="image-title">Probabilidad Condicional</div>
        <div class="status" id="status-probabilidad">Cargando...</div>
    </div>
    
    <div class="image-container">
        <img src="images/ejercicio-parabrisas.svg" alt="Ejercicio Parabrisas" onload="showStatus(this, true)" onerror="showStatus(this, false)">
        <div class="image-title">Parabrisas Geométrico</div>
        <div class="status" id="status-parabrisas">Cargando...</div>
    </div>
    
    <div class="image-container">
        <img src="images/ejercicio-descuentos.svg" alt="Ejercicio Descuentos" onload="showStatus(this, true)" onerror="showStatus(this, false)">
        <div class="image-title">Descuentos y Porcentajes</div>
        <div class="status" id="status-descuentos">Cargando...</div>
    </div>
    
    <div class="image-container">
        <img src="images/placeholder.svg" alt="Placeholder" onload="showStatus(this, true)" onerror="showStatus(this, false)">
        <div class="image-title">Placeholder</div>
        <div class="status" id="status-placeholder">Cargando...</div>
    </div>
    
    <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 10px;">
        <h3>📋 Instrucciones:</h3>
        <ol>
            <li>Todas las imágenes deben mostrar "✅ Cargada correctamente"</li>
            <li>Si alguna muestra "❌ Error al cargar", verifica la ruta del archivo</li>
            <li>Las imágenes son SVG vectoriales, por lo que se ven nítidas en cualquier tamaño</li>
            <li>Una vez verificado, puedes eliminar este archivo test-images.html</li>
        </ol>
        
        <h3>🔧 Solución de problemas:</h3>
        <ul>
            <li><strong>Error 404:</strong> Verifica que la carpeta images/ existe y contiene los archivos SVG</li>
            <li><strong>Imagen en blanco:</strong> Revisa el contenido del archivo SVG</li>
            <li><strong>No se ve en el portafolio:</strong> Verifica las rutas en ejercicios-data.js</li>
        </ul>
        
        <p><a href="index.html" style="color: #3498db; text-decoration: none; font-weight: bold;">← Volver al Portafolio Principal</a></p>
    </div>
    
    <script>
        function showStatus(img, success) {
            const imageName = img.src.split('/').pop().split('.')[0].replace('ejercicio-', '');
            const statusId = 'status-' + imageName;
            const statusElement = document.getElementById(statusId);
            
            if (success) {
                statusElement.textContent = '✅ Cargada correctamente';
                statusElement.className = 'status success';
            } else {
                statusElement.textContent = '❌ Error al cargar';
                statusElement.className = 'status error';
            }
        }
        
        // Verificar después de 3 segundos si alguna imagen no se cargó
        setTimeout(() => {
            const statusElements = document.querySelectorAll('.status');
            let allLoaded = true;
            let errorCount = 0;
            
            statusElements.forEach(status => {
                if (status.textContent === 'Cargando...') {
                    status.textContent = '❌ Timeout - No se cargó';
                    status.className = 'status error';
                    allLoaded = false;
                    errorCount++;
                }
                if (status.textContent.includes('❌')) {
                    errorCount++;
                    allLoaded = false;
                }
            });
            
            if (allLoaded) {
                console.log('✅ Todas las imágenes se cargaron correctamente');
                document.title = '✅ Test Exitoso - Portafolio R-exams';
            } else {
                console.log(`❌ ${errorCount} imágenes fallaron al cargar`);
                document.title = `❌ ${errorCount} Errores - Portafolio R-exams`;
            }
        }, 3000);
    </script>
</body>
</html>
